defmodule EventsService.Events.SalesChannel do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Ecto.Multi
  alias EventsService.Channels
  alias EventsService.Channels.ChannelConfig
  alias EventsService.Events
  alias EventsService.Events.Event
  alias EventsService.Events.SalesChannel
  alias EventsService.Events.TicketCategory
  alias EventsService.Events.Variant
  alias EventsService.Events.VariantCounter
  alias EventsService.Offers
  alias EventsService.Offers.Availability
  alias EventsService.Repo
  alias EventsService.Util.Token

  require Logger

  @quota_modes [:RESERVED, :SHARED]

  # styler:sort
  @type t ::
          %__MODULE__{
            channel_config: ChannelConfig.t() | nil,
            channel_config_id: Ecto.UUID.t(),
            deleted_at: DateTime.t() | nil,
            id: Ecto.UUID.t(),
            inserted_at: DateTime.t(),
            original_price: integer(),
            updated_at: DateTime.t(),
            variant: Variant.t(),
            variant_id: Ecto.UUID.t(),
            quota_mode: atom()
          }

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "sales_channels" do
    field :original_price, :integer
    field :deleted_at, :utc_datetime
    field :quota_mode, Ecto.Enum, values: @quota_modes, default: :RESERVED

    belongs_to :variant, Variant
    belongs_to :channel_config, ChannelConfig

    timestamps()
  end

  @doc false
  def changeset(sales_channel, attrs) do
    sales_channel
    |> cast(attrs, [:variant_id, :channel_config_id, :original_price, :quota_mode])
    |> validate_required([:variant_id, :channel_config_id, :original_price])
    |> validate_inclusion(:quota_mode, @quota_modes)
  end

  def update_changeset(sales_channel, attrs) do
    sales_channel
    |> cast(attrs, [:original_price, :quota_mode])
    |> validate_required([:original_price])
    |> validate_inclusion(:quota_mode, @quota_modes)
  end

  def delete_changeset(sales_channel, attrs) do
    sales_channel
    |> cast(attrs, [:deleted_at])
    |> validate_required([:deleted_at])
  end

  def get_by_variant_id(variant_id) do
    query =
      from(sc in SalesChannel,
        where: sc.variant_id == ^variant_id and is_nil(sc.deleted_at)
      )

    Repo.one(query)
  end

  @spec get_by_ticket_category_id(ticket_category_id :: Ecto.UUID.t()) ::
          [SalesChannel.t()]
  def get_by_ticket_category_id(ticket_category_id) do
    query =
      from(sc in SalesChannel,
        as: :sales_channel,
        inner_join: v in Variant,
        as: :variant,
        on: sc.variant_id == v.id,
        inner_join: tc in TicketCategory,
        as: :ticket_category,
        on: v.ticket_category_id == tc.id,
        where: tc.id == ^ticket_category_id and is_nil(tc.deleted_at),
        where: is_nil(v.deleted_at),
        where: is_nil(sc.deleted_at),
        select: sc
      )

    Repo.all(query)
  end

  def list_sales_channels(event_id, page, page_size, search_string, order_by, direction) do
    sales_channel_query()
    |> filter_by_event_id(event_id)
    |> filter_by_search_string(search_string)
    |> sort_by(order_by, direction)
    |> paginate(page, page_size)
    |> Repo.all()
  end

  @spec get_by_event_id(event_id :: Ecto.UUID.t()) :: [SalesChannel.t()]
  @spec get_by_event_id(event_id :: Ecto.UUID.t(), preloads :: [atom()]) :: [SalesChannel.t()]
  def get_by_event_id(event_id, preloads \\ []) do
    Repo.all(
      from(sales_channel in SalesChannel,
        as: :sales_channel,
        join: channel_config in assoc(sales_channel, :channel_config),
        as: :channel_config,
        on: sales_channel.channel_config_id == channel_config.id,
        preload: ^preloads,
        where: channel_config.event_id == ^event_id,
        where: is_nil(sales_channel.deleted_at),
        where: is_nil(channel_config.deleted_at)
      )
    )
  end

  def get_sales_channel(id, preloads \\ []) do
    result =
      sales_channel_query()
      |> where([sc], sc.id == ^id)
      |> preload(^preloads)
      |> Repo.one()

    case result do
      nil -> {:error, "Sales channel not found"}
      sales_channel -> {:ok, sales_channel}
    end
  end

  @spec sales_channels_statistics(Ecto.UUID.t()) ::
          {:ok,
           %{
             total_quota: integer(),
             total_sales_channel_quota: integer(),
             total_quota_sold: integer(),
             total_sales: integer()
           }}
          | {:error, atom()}
  def sales_channels_statistics(event_id) do
    case Events.get_event(
           event_id,
           [:ticket_categories, variants: [:variant_counter, sales_channel: [:channel_config]]]
         ) do
      nil ->
        {:error, :unknown_event}

      event ->
        total_quota = calculate_total_quota(event)
        total_sales_channel_quota = calculate_total_sales_channel_quota(event)
        total_quota_sold = calculate_total_quota_sold(event)

        total_sales = calculate_total_sales(event)

        {:ok,
         %{
           total_quota: total_quota,
           total_sales_channel_quota: total_sales_channel_quota,
           total_quota_sold: total_quota_sold,
           total_sales: total_sales
         }}
    end
  end

  def sales_channel_query do
    from(sc in SalesChannel,
      where: is_nil(sc.deleted_at),
      join: cc in ChannelConfig,
      on: sc.channel_config_id == cc.id,
      preload: [
        :channel_config,
        variant: [:availability, :variant_counter, :ticket_category]
      ],
      group_by: [
        sc.id,
        cc.id,
        cc.token,
        cc.value,
        cc.type,
        cc.valid_until,
        cc.label,
        cc.description,
        cc.color,
        cc.amount_of_objects
      ]
    )
  end

  def create_sales_channel(%{"variant_id" => variant_id, "event_id" => event_id} = attrs) do
    Logger.debug("Create Sales Channel for event #{event_id} and use Variant #{variant_id} as example")

    channel_configs_attrs = prepare_channel_configs_attrs(attrs)
    multi = Ecto.Multi.new()

    quota_mode =
      if Map.has_key?(attrs, "quota_mode"), do: attrs["quota_mode"], else: :RESERVED

    with {_, %Variant{ticket_category_id: ticket_category_id}} <-
           {:variant, Variant.get_by_id(variant_id, [])},
         {_, %Event{} = event} <- {:event, Events.get_event(event_id)},
         {_, :ok} <- {:validate_event_not_closed_or_draft, Event.validate_event_not_closed_or_draft(event)},
         attrs =
           Map.put(attrs, "ticket_category_id", ticket_category_id),
         multi =
           multi
           |> TicketCategory.add_quota_to_multi(ticket_category_id, attrs["quota"], quota_mode)
           |> Offers.maybe_insert_availability_to_multi(
             event_id,
             DateTime.utc_now(),
             attrs["valid_from"]
           )
           |> Variant.insert_variant_to_multi(prepare_variants_attrs(attrs))
           |> Channels.insert_channel_config_to_multi(channel_configs_attrs)
           |> insert_sales_channel_to_multi(attrs),
         {_, {:ok, %{sales_channel: sales_channel, update_ticket_category: ticket_category}}} <-
           {:transaction, Repo.transaction(multi)} do
      {:ok, sales_channel, ticket_category}
    else
      {:variant, nil} ->
        {:error, :variant_not_found}

      {:event, nil} ->
        {:error, :event_not_found}

      {:validate_event_not_closed_or_draft, _} ->
        {:error, :event_already_closed}

      {:transaction, {:error, failed_operation, failed_value, _changes_so_far}} ->
        Logger.error(
          "Failed to create sales channel in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_operation, failed_value}
    end
  end

  @spec update_sales_channel(map(), SalesChannel.t(), Ecto.UUID.t()) ::
          {:ok, ChannelConfig.t()}
          | {:ok, SalesChannel.t()}
          | {:error, atom(), any()}
          | {:error, atom()}
  def update_sales_channel(
        attrs,
        %SalesChannel{
          variant:
            %{
              id: variant_id,
              availability: %Availability{} = availability,
              ticket_category: %TicketCategory{id: ticket_category_id},
              variant_counter: variant_counter
            } = variant,
          channel_config: %ChannelConfig{} = channel_config
        } = sales_channel,
        event_id
      ) do
    Logger.debug("Create Sales Channel for event #{event_id} and use Variant #{variant_id} as example")

    with {_, %Event{} = event} <- {:event, Events.get_event(event_id)},
         {_, :ok} <- {:validate_event_not_closed_or_draft, Event.validate_event_not_closed_or_draft(event)},
         sales = sales_channel_sales(variant_counter),
         channel_config_attrs = prepare_channel_configs_update_attrs(attrs, sales),
         variants_attrs =
           prepare_variant_update_attrs(attrs, sales_channel, channel_config, sales),
         availability_attrs = prepare_availability_attrs(attrs),
         sales_channel_attrs = prepare_sales_channel_update_attrs(attrs, sales),
         multi =
           Ecto.Multi.new()
           |> Channels.maybe_update_channel_config_to_multi(channel_config, channel_config_attrs)
           |> Offers.maybe_update_availability_to_multi(availability, availability_attrs)
           |> Variant.maybe_sales_channel_update_variant_to_multi(variant, variants_attrs)
           |> maybe_update_ticket_category_to_multi(ticket_category_id, sales_channel, channel_config, attrs)
           |> maybe_update_sales_channel_to_multi(sales_channel, sales_channel_attrs),
         {_, {:ok, transaction_response}} <- {:transaction, Repo.transaction(multi)} do
      case transaction_response do
        %{update_sales_channel: updated_sales_channel} -> {:ok, updated_sales_channel}
        _ -> {:ok, sales_channel}
      end
    else
      {:event, nil} ->
        {:error, :event_not_found}

      {:validate_event_not_closed_or_draft, _} ->
        {:error, :event_already_closed}

      {:editable_sales_channel, false} ->
        {:error, :sales_channel_not_editable}

      {:transaction, {:error, failed_operation, failed_value, _changes_so_far}} ->
        Logger.error(
          "Failed to update sales channel in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_operation, failed_value}
    end
  end

  @spec delete_sales_channel(sales_channel :: SalesChannel.t()) ::
          {:ok, SalesChannel.t()}
          | {:error, atom(), any()}
  def delete_sales_channel(
        %{id: id, channel_config: %{amount_of_objects: cc_quota}, variant: %{ticket_category: ticket_category}} =
          _sales_channel
      ) do
    result =
      Multi.new()
      |> Multi.run(:get_sales_channel, fn repo, _changes -> get_sales_channel_by_id(repo, id) end)
      |> Multi.run(:get_channel_config, fn repo, _changes -> get_channel_config(repo, id) end)
      |> Multi.run(:get_variant, fn repo, %{get_sales_channel: sales_channel} = _changes ->
        get_variant(repo, sales_channel.variant_id)
      end)
      |> Multi.update(:delete_channel_config, fn %{get_channel_config: channel_config} ->
        ChannelConfig.delete_changeset(channel_config, %{deleted_at: DateTime.utc_now()})
      end)
      |> Multi.update(:delete_sales_channel, fn %{get_sales_channel: sales_channel} ->
        SalesChannel.delete_changeset(sales_channel, %{
          deleted_at: DateTime.utc_now()
        })
      end)
      |> Multi.update(:delete_variant, fn %{get_variant: variant} ->
        Variant.delete_changeset(variant, %{deleted_at: DateTime.utc_now()})
      end)
      |> TicketCategory.add_quota_to_multi(ticket_category, -cc_quota, :RESERVED)
      |> Repo.transaction()

    case result do
      {:ok,
       %{
         get_sales_channel: sales_channel
       }} ->
        {:ok, sales_channel}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error(
          "Failed to delete sales channel in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_operation, failed_value}
    end
  end

  def revoke_channel_url(id) do
    result =
      Multi.new()
      |> Multi.run(:get_channel_config, fn repo, _changes ->
        get_channel_config(repo, id)
      end)
      |> Multi.update(:update_channel_token, fn %{get_channel_config: channel_config} ->
        ChannelConfig.revoke_token_changeset(channel_config, %{
          token: generate_channel_configs_token(channel_config.event_id)
        })
      end)
      |> Repo.transaction()

    case result do
      {:ok, %{update_channel_token: channel_config}} ->
        {:ok, channel_config}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error(
          "Failed to update sales channel token in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_operation, failed_value}
    end
  end

  def generate_channel_configs_token(event_id) do
    token = Token.generate_token()

    query =
      from(cc in ChannelConfig,
        where: cc.event_id == ^event_id and cc.token == ^token
      )

    case Repo.one(query) do
      nil -> token
      _ -> generate_channel_configs_token(event_id)
    end
  end

  defp insert_sales_channel_to_multi(multi, %{"original_price" => original_price} = _attrs) do
    Multi.insert(multi, :sales_channel, fn %{
                                             insert_variant: variant,
                                             insert_channel_config: channel_config
                                           } ->
      SalesChannel.changeset(
        %SalesChannel{},
        %{
          "original_price" => round(original_price),
          "variant_id" => variant.id,
          "channel_config_id" => channel_config.id
        }
      )
    end)
  end

  defp insert_sales_channel_to_multi(multi, %{"unit_price" => original_price} = attrs),
    do: insert_sales_channel_to_multi(multi, Map.put(attrs, "original_price", original_price))

  defp maybe_update_sales_channel_to_multi(multi, sales_channel, %{"unit_price" => unit_price} = attrs),
    do:
      Multi.update(
        multi,
        :update_sales_channel,
        update_changeset(sales_channel, Map.put(attrs, "original_price", unit_price))
      )

  defp maybe_update_sales_channel_to_multi(multi, %{id: sales_channel_id} = sales_channel, attrs) do
    Logger.debug("Updating sales channel #{sales_channel_id} to multi with attrs #{inspect(attrs)}")

    Multi.update(multi, :update_sales_channel, update_changeset(sales_channel, attrs))
  end

  # maybe update ticket category during update a sales channel
  # Following cases are handled:
  # 1. Update quota and quota_mode
  #    (reduce the old quota from the old quota_mode and add the new quota to the new quota mode)
  # 2. Update quota but keeps quota_mode
  #    (add the  difference between the new quota and the old quota to the old quota mode)
  # 3. Update quota_mode but keeps quota
  #    (reduce the old quota from the old quota mode and add the old quota to the new quota mode)
  # 4. Do nothing
  defp maybe_update_ticket_category_to_multi(
         multi,
         ticket_category_id,
         %{quota_mode: sc_quota_mode} = _sales_channel,
         %{amount_of_objects: cc_quota} = _channel_config,
         %{"quota" => new_quota, "quota_mode" => new_quota_mode} = _attrs
       ) do
    multi
    |> TicketCategory.add_quota_to_multi(ticket_category_id, -cc_quota, sc_quota_mode)
    |> TicketCategory.add_quota_to_multi(ticket_category_id, new_quota, new_quota_mode)
  end

  defp maybe_update_ticket_category_to_multi(
         multi,
         ticket_category_id,
         %{quota_mode: sc_quota_mode} = _sales_channel,
         %{amount_of_objects: cc_quota} = _channel_config,
         %{"quota" => new_quota} = _attrs
       ) do
    TicketCategory.add_quota_to_multi(multi, ticket_category_id, new_quota - cc_quota, sc_quota_mode)
  end

  defp maybe_update_ticket_category_to_multi(
         multi,
         ticket_category_id,
         %{quota_mode: sc_quota_mode} = _sales_channel,
         %{amount_of_objects: cc_quota} = _channel_config,
         %{"quota_mode" => new_quota_mode} = _attrs
       ) do
    multi
    |> TicketCategory.add_quota_to_multi(ticket_category_id, -cc_quota, sc_quota_mode)
    |> TicketCategory.add_quota_to_multi(ticket_category_id, cc_quota, new_quota_mode)
  end

  defp maybe_update_ticket_category_to_multi(multi, _ticket_category_id, _sales_channel, _channel_config, _attrs),
    do: multi

  defp prepare_variants_attrs(attrs) do
    attrs
    |> Map.put(
      "unit_price",
      calculate_variant_price(attrs)
    )
    |> Map.put("visibility_after_sales_ended", "VISIBLE_WITH_PRICE_AND_DATE")
    |> Map.put("distribution_type", "SALES_CHANNEL")
  end

  # TODO: Clean up calculate_variant_price and calculate_variant_update_price
  defp calculate_variant_price(%{"type" => "fixed", "unit_price" => unit_price, "value" => value}),
    do: unit_price - value

  defp calculate_variant_price(%{"type" => "fixed", "original_price" => original_price, "value" => value}),
    do: original_price - value

  defp calculate_variant_price(%{"type" => "percentage", "unit_price" => unit_price, "value" => value}),
    do: round(unit_price - unit_price * (value / 100 / 100))

  defp calculate_variant_price(%{"type" => "percentage", "original_price" => original_price, "value" => value}),
    do: round(original_price - original_price * (value / 100 / 100))

  defp calculate_variant_price(_attrs), do: nil

  defp calculate_variant_update_price(
         %{"type" => "fixed", "original_price" => original_price} = attrs,
         _sales_channel,
         channel_config
       )
       when is_integer(original_price) do
    value = Map.get(attrs, "value", channel_config.value)
    original_price - value
  end

  defp calculate_variant_update_price(%{"type" => "fixed"} = attrs, sales_channel, channel_config) do
    value = Map.get(attrs, "value", channel_config.value)
    original_price = Map.get(attrs, "unit_price", sales_channel.original_price)
    original_price - value
  end

  defp calculate_variant_update_price(
         %{"type" => "percentage", "original_price" => original_price} = attrs,
         _sales_channel,
         channel_config
       )
       when is_integer(original_price) do
    value = Map.get(attrs, "value", channel_config.value)
    original_price - original_price * (value / 100 / 100)
  end

  defp calculate_variant_update_price(%{"type" => "percentage"} = attrs, sales_channel, channel_config) do
    value = Map.get(attrs, "value", channel_config.value)
    original_price = Map.get(attrs, "unit_price", sales_channel.original_price)
    original_price - original_price * (value / 100 / 100)
  end

  defp calculate_variant_update_price(attrs, sales_channel, channel_config) do
    type = attrs |> Map.get("type", channel_config.type) |> Atom.to_string()

    if type in ["fixed", "percentage"] do
      attrs = Map.put(attrs, "type", type)
      calculate_variant_update_price(attrs, sales_channel, channel_config)
    else
      {:error, :unknown_type}
    end
  end

  def validate_ticket_category(%{"ticket_category_id" => id}, event_id) do
    query =
      from(tc in TicketCategory,
        where: tc.id == ^id and is_nil(tc.deleted_at) and tc.event_id == ^event_id,
        select: tc.id
      )

    case Repo.one(query) do
      nil -> {:error, "Ticket category not found"}
      _ -> {:ok, nil}
    end
  end

  def validate_ticket_category(_, _), do: {:ok, nil}

  defp sales_channel_sales(nil), do: 0
  defp sales_channel_sales(%{sold: sold}), do: sold
  defp sales_channel_sales(_variant_counter), do: 0

  defp prepare_channel_configs_attrs(attrs) do
    quota_mode =
      if Map.has_key?(attrs, "quota_mode"), do: attrs["quota_mode"], else: :RESERVED

    %{
      "token" => generate_channel_configs_token(attrs["event_id"]),
      "event_id" => attrs["event_id"],
      "type" => attrs["type"],
      "value" => attrs["value"],
      "valid_until" => attrs["valid_until"],
      "label" => attrs["label"],
      "amount_of_objects" => attrs["quota"],
      "quota_mode" => quota_mode
    }
  end

  defp prepare_channel_configs_update_attrs(attrs, 0) do
    %{}
    |> maybe_set_key("type", attrs["type"])
    |> maybe_set_key("value", attrs["value"])
    |> maybe_set_key("valid_until", attrs["valid_until"])
    |> maybe_set_key("label", attrs["label"])
    |> maybe_set_key("amount_of_objects", attrs["quota"])
    |> maybe_set_key("quota_mode", attrs["quota_mode"])
  end

  defp prepare_channel_configs_update_attrs(%{"quota" => quota} = attrs, sales)
       when not is_nil(quota) and quota >= sales,
       do: maybe_set_key(%{"amount_of_objects" => quota}, "valid_until", attrs["valid_until"])

  defp prepare_channel_configs_update_attrs(attrs, _sales), do: maybe_set_key(%{}, "valid_until", attrs["valid_until"])

  defp prepare_variant_update_attrs(attrs, sales_channel, channel_config, 0) do
    unit_price = calculate_variant_update_price(attrs, sales_channel, channel_config)

    %{}
    |> maybe_set_key("quota", attrs["quota"])
    |> maybe_set_key("ticket_category_id", attrs["ticket_category_id"])
    |> maybe_set_key("unit_price", round(unit_price))
  end

  defp prepare_variant_update_attrs(%{"quota" => quota} = _attrs, _sales_channel, _channel_config, sales)
       when not is_nil(quota) and quota >= sales do
    %{"quota" => quota}
  end

  defp prepare_variant_update_attrs(_attrs, _sales_channel, _channel_config, _sales), do: %{}

  defp prepare_sales_channel_update_attrs(%{"original_price" => original_price} = _attrs, 0) do
    maybe_set_key(%{}, "original_price", original_price)
  end

  defp prepare_sales_channel_update_attrs(_attrs, _sales), do: %{}

  defp prepare_availability_attrs(%{"valid_from" => valid_from, "valid_until" => valid_until}) do
    %{"valid_from" => valid_from, "valid_until" => valid_until}
  end

  defp prepare_availability_attrs(%{"valid_from" => valid_from}) do
    %{"valid_from" => valid_from}
  end

  defp prepare_availability_attrs(%{"valid_until" => valid_until}) do
    %{"valid_until" => valid_until}
  end

  defp prepare_availability_attrs(_attrs), do: %{}

  defp get_channel_config(repo, sales_channel_id) do
    query =
      from(sc in SalesChannel,
        where: sc.id == ^sales_channel_id and is_nil(sc.deleted_at),
        preload: [:channel_config]
      )

    case repo.one(query) do
      nil -> {:error, "Channel config not found"}
      sales_channel -> {:ok, sales_channel.channel_config}
    end
  end

  defp get_sales_channel_by_id(repo, sales_channel_id) do
    query =
      from(sc in SalesChannel,
        where: sc.id == ^sales_channel_id and is_nil(sc.deleted_at)
      )

    case repo.one(query) do
      nil -> {:error, "Sales Channel not found"}
      sales_channel -> {:ok, sales_channel}
    end
  end

  defp get_variant(repo, variant_id) do
    query =
      from(variant in Variant,
        where: variant.id == ^variant_id and is_nil(variant.deleted_at),
        preload: [:variant_counter]
      )

    case repo.one(query) do
      nil ->
        {:error, "Variant not found"}

      variant ->
        case variant.variant_counter do
          nil -> {:ok, variant}
          %VariantCounter{sold: 0} -> {:ok, variant}
          _ -> {:error, "Variant has sales"}
        end
    end
  end

  defp maybe_set_key(map, _key, nil), do: map
  defp maybe_set_key(map, key, value), do: Map.put(map, key, value)

  defp filter_by_event_id(query, nil), do: query

  defp filter_by_event_id(query, event_id) do
    where(query, [_sc, channel], channel.event_id == ^event_id)
  end

  defp paginate(query, page, page_size) do
    offset = page * page_size

    query
    |> limit(^page_size)
    |> offset(^offset)
  end

  defp sort_by(query, nil, nil), do: sort_by_channel_config_prop(query, :label, :asc)

  defp sort_by(query, order_by, nil) when order_by in [:label, :valid_until, :value],
    do: sort_by_channel_config_prop(query, order_by, :asc)

  defp sort_by(query, nil, direction) when direction in [:asc, :desc],
    do: sort_by_channel_config_prop(query, :label, direction)

  defp sort_by(query, order_by, direction)
       when order_by in [:label, :valid_until, :value] and direction in [:asc, :desc],
       do: sort_by_channel_config_prop(query, order_by, direction)

  defp sort_by(query, _order_by, _direction), do: query

  defp sort_by_channel_config_prop(query, order_by, direction) do
    query
    |> order_by([_sc, channel], [
      {^direction, field(channel, ^order_by)}
    ])
    |> group_by([_sc, channel], field(channel, ^order_by))
  end

  defp filter_by_search_string(query, nil), do: query

  defp filter_by_search_string(query, search_string) do
    like_search_term = "%#{search_string}%"

    where(
      query,
      [_sc, channel],
      ilike(channel.label, ^like_search_term) or ilike(channel.description, ^like_search_term)
    )
  end

  defp calculate_total_quota(event) do
    Enum.reduce(event.ticket_categories, 0, fn item, acc ->
      if is_nil(item.deleted_at) do
        acc + item.quota
      else
        acc
      end
    end)
  end

  defp calculate_total_sales_channel_quota(event) do
    Enum.reduce(event.variants, 0, fn item, acc ->
      if not is_nil(item.sales_channel) and
           is_nil(item.sales_channel.deleted_at) and
           is_nil(item.sales_channel.channel_config.channel_key) do
        acc + item.quota
      else
        acc
      end
    end)
  end

  defp calculate_total_quota_sold(event) do
    Enum.reduce(event.variants, 0, fn item, acc ->
      if not is_nil(item.sales_channel) and
           is_nil(item.sales_channel.deleted_at) and
           is_nil(item.sales_channel.channel_config.channel_key) do
        acc +
          if is_nil(item.variant_counter) do
            0
          else
            item.variant_counter.sold
          end
      else
        acc
      end
    end)
  end

  defp calculate_total_sales(event) do
    Enum.reduce(event.variants, 0, fn item, acc ->
      if not is_nil(item.sales_channel) and
           is_nil(item.sales_channel.deleted_at) and
           is_nil(item.sales_channel.channel_config.channel_key) do
        acc +
          if is_nil(item.variant_counter) do
            0
          else
            item.variant_counter.total_sales
          end
      else
        acc
      end
    end)
  end
end
