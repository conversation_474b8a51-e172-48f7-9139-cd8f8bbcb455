defmodule EventsService.Guestlists.InvitationOrderHelper do
  @moduledoc false
  alias ExServiceClient.Services.OrdersService

  require Logger

  def get_tickets_by_invitation_id(invitation_id) do
    case OrdersService.get_by_invitation_id(invitation_id) do
      {:ok, tickets} -> {:tickets, {:ok, tickets}}
      error -> {:tickets, {:error, error}}
    end
  end

  def create_order(invitation, claim_params, personal_information_attendees) do
    create_order_params =
      build_create_order_params(invitation, claim_params, personal_information_attendees)

    case OrdersService.create_order(create_order_params) do
      {:ok, result} -> {:create_order_result, {:ok, result["order_id"]}}
      error -> {:create_order_result, {:error, error}}
    end
  end

  def update_order(invitation) do
    case invitation.invitation_approval_requests do
      [] ->
        nil

      requests ->
        order_ids =
          requests
          |> Enum.map(fn request -> request.order_id end)
          |> Enum.filter(fn order_id -> order_id != nil end)

        update_created_orders(invitation, order_ids)
    end
  end

  defp update_created_orders(invitation, order_ids) do
    Enum.each(order_ids, fn order_id -> update_created_order(invitation, order_id) end)
  end

  defp update_created_order(invitation, order_id) do
    params = %{
      "invitation_id" => invitation.id,
      "invitation_status" => "REJECTED"
    }

    case OrdersService.update_order(order_id, params) do
      {:ok, _update_result} -> {:update_order_result, :ok}
      error -> {:create_order_result, error}
    end
  end

  def extend_by_tickets(invitation, [] = _tickets), do: invitation

  def extend_by_tickets(invitation, tickets) do
    extended_invitation_approval_requests =
      case invitation.invitation_approval_requests do
        nil -> nil
        requests -> extend_requests_by_tickets(requests, tickets)
      end

    Map.put(invitation, :invitation_approval_requests, extended_invitation_approval_requests)
  end

  defp extend_requests_by_tickets(requests, tickets) do
    Enum.map(requests, fn request ->
      extended_attendees = extend_attendees_by_tickets(request.attendees, tickets)
      Map.put(request, :attendees, extended_attendees)
    end)
  end

  defp extend_attendees_by_tickets(attendees, tickets) do
    Enum.map(attendees, fn attendee ->
      attendee_ticket =
        Enum.find(tickets["tickets"], fn ticket ->
          ticket["ticket"]["attendee"]["id"] == attendee.personal_information.id
        end)

      Map.merge(
        %{
          ticket_id: attendee_ticket["ticket"]["id"],
          is_checkin_allowed: attendee_ticket["ticket"]["is_checkin_allowed"],
          check_in_date: attendee_ticket["ticket"]["check_in_date"],
          status: attendee_ticket["ticket"]["status"]
        },
        attendee
      )
    end)
  end

  defp build_create_order_params(invitation, claim_params, personal_information_attendees) do
    %{
      "items" => build_items(invitation, claim_params),
      "invitation" => build_invitation(invitation, claim_params),
      "userinfo" => build_user_info(claim_params),
      "attendees" => build_attendees(personal_information_attendees),
      "email" => claim_params["guest_information"]["email"],
      "amount" => length(claim_params["attendees"]),
      "user_id" => claim_params["userId"]
    }
  end

  defp build_items(invitation, claim_params) do
    %{
      "amount" => length(claim_params["attendees"]),
      "eventId" => invitation.guestlist.event_id,
      "categoryId" => invitation.variant.ticket_category_id,
      "variantId" => invitation.variant.id,
      "donate" => false
    }
  end

  defp build_invitation(invitation, claim_params) do
    %{
      "id" => invitation.id,
      "status" => :ACCEPTED,
      "is_auto_approve" => true,
      "variant_id" => invitation.variant.id,
      "quota" => length(claim_params["attendees"]),
      "event_id" => invitation.guestlist.event_id,
      "ticket_category_id" => invitation.variant.ticket_category_id,
      "guestlist_id" => invitation.guestlist_id,
      "email_receiver_id" => invitation.email_receiver_id,
      "variant_name" => invitation.variant.ticket_category.name,
      "max_requests_per_email" => nil,
      "valid_until" => nil,
      "valid_from" => nil,
      "admission" => invitation.variant.ticket_category.admission
    }
  end

  defp build_user_info(claim_params) do
    %{
      "personalInformation" => %{
        "birthdate" => claim_params["guest_information"]["birthdate"],
        "email" => claim_params["guest_information"]["email"],
        "givenName" => claim_params["guest_information"]["given_name"],
        "gender" => claim_params["guest_information"]["gender"],
        "familyName" => claim_params["guest_information"]["family_name"]
      }
    }
  end

  defp build_attendees(personal_information_attendees) do
    Enum.map(personal_information_attendees, fn attendee ->
      %{
        "id" => attendee.id,
        "name" => attendee.given_name
      }
    end)
  end
end
