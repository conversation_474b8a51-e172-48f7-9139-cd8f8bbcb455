defmodule EventsServiceWeb.EventController do
  @moduledoc false
  use EventsServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs
  use Params

  alias EventsService.EntranceAreas
  alias EventsService.EventPermissions
  alias EventsService.Events
  alias EventsService.Events.Event
  alias EventsService.Events.EventPermission
  alias EventsService.Events.Workflow
  alias EventsService.Seatsio
  alias EventsService.Token
  alias EventsService.Tracking
  alias EventsService.Util.EventPublicationHelper
  alias EventsService.Util.Mailer
  alias EventsService.Validators.EventCreationValidator
  alias EventsService.Validators.EventPermissionValidator
  alias EventsService.Vendor
  alias EventsService.Vendor.Promoter
  alias EventsServiceWeb.ApiSchemas.EventSchema.Event, as: EventResponse
  alias EventsServiceWeb.ApiSchemas.EventSchema.EventParams
  alias EventsServiceWeb.ApiSpecs.SellerSpecs
  alias EventsServiceWeb.ChangesetJSON
  alias EventsServiceWeb.ParamSchemas.SellerParams
  alias EventsServiceWeb.Plugs.Authorize
  alias ExServiceClient.Services.AccountsService
  alias OpenApiSpex.Reference

  require Logger

  plug Authorize, [rule: ["promoter", "event", "write"], permission: "event.create"] when action in [:create]

  plug Authorize,
       [rule: ["promoter", "event", "write"], permission: "event.edit"]
       when action in [:update, :create_permissions, :update_permissions, :delete_permissions]

  plug Authorize, [rule: ["promoter", "event", "read"], permission: "event.view"] when action in [:spotify]

  action_fallback EventsServiceWeb.FallbackController

  tags ["Sellers"]
  operation :index, SellerSpecs.events_operation()

  def index(%{assigns: %{user_payload: %{"affiliation" => seller_id}}} = conn, %{"seller_id" => seller_id} = params) do
    case {:params, SellerParams.events_params(params)} do
      {_, %{valid?: true} = changeset} ->
        validated_params = Params.to_map(changeset)
        result = Events.get_seller_events(validated_params)

        conn
        |> put_status(:ok)
        |> render(:index,
          events: result.entries,
          meta: %{
            page: result.page_number,
            pageSize: result.page_size,
            totalPages: result.total_pages,
            totalEntries: result.total_entries
          }
        )

      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)
    end
  end

  tags ["Events"]

  def index(conn, %{"type" => "popular"} = params) do
    page_size =
      case params["size"] do
        nil -> 3
        _size -> String.to_integer(params["size"])
      end

    promoter_id = params["promoterId"]

    case Events.get_popular_events(page_size, promoter_id) do
      nil -> render(conn, :to_event_overview_dto, events: [])
      events -> render(conn, :to_event_overview_dto, events: events)
    end
  end

  def index(conn, %{"eventIds" => ""}) do
    render(conn, :to_event_overview_dto, events: [])
  end

  def index(conn, %{"eventIds" => event_ids, "scope" => "service"}) do
    with [api_token | _] <- get_req_header(conn, "x-api-token"),
         {:ok, _claim} <- ExServiceClient.Token.verify_and_validate(api_token) do
      events = Events.get_list_of_events(String.split(event_ids, ","))
      render(conn, :to_internal_events_details_dto, events: events)
    else
      [] ->
        Logger.error("Service authentication failed because of missing token")

        conn
        |> put_status(:forbidden)
        |> json(%{error: "Missing token"})

      {:error, msg} ->
        Logger.error("Service authentication failed because of #{inspect(msg)}")

        conn
        |> put_status(:forbidden)
        |> json(%{error: msg})
    end
  end

  defparams(
    promoter_store_params(%{
      promoterId!: Ecto.UUID,
      sort: [
        field: Ecto.Enum,
        values: [:title, :startDate, :popular, :price]
      ],
      order: [field: Ecto.Enum, values: [:asc, :desc], default: :asc],
      page: [field: :integer, default: 0],
      page_size: [field: :integer, default: 20]
    })
  )

  def index(%{private: %{experimental: true}} = conn, %{"promoterId" => promoter_id} = params) do
    Logger.debug("Getting  promoter with following id #{promoter_id}")

    with {_, %{valid?: true} = changeset} <- {:params, promoter_store_params(params)},
         {_, events} <- {:events, Events.list_promoter_events(promoter_id, Params.to_map(changeset))} do
      events_dto = EventsServiceWeb.EventJSON.to_event_overview_dto(%{page: events})
      json(conn, events_dto)
    else
      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:events, _error} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{
          message: "Unknown database error during promoter event fetching",
          error_code: :unknown_database_error
        })
    end
  end

  def index(conn, params) do
    offset_document_id = params["lastDocumentId"]
    page_size = String.to_integer(params["size"] || "10")
    category_name = params["categoryNames"]
    promoter_id = params["promoterId"]
    venue_id = params["venueId"]
    venue_city = params["venueCity"]
    event_ids = params["eventIds"]
    render_type = params["render"]

    events =
      case event_ids do
        nil ->
          Events.list_events(
            offset_document_id,
            page_size,
            category_name,
            promoter_id,
            venue_id,
            venue_city
          )

        event_ids ->
          Events.get_list_of_events(String.split(event_ids, ","))
      end

    Logger.info("List of events for category #{inspect(category_name)} found #{Enum.count(events)} events")

    case render_type do
      "complete" -> render(conn, :to_event_complete_dto, events: events)
      _ -> render(conn, :to_event_overview_dto, events: events)
    end
  end

  # TODO return algolia compatible result to fix asap
  def search(conn, %{"requests" => [%{"indexName" => index, "params" => %{"query" => query}}]} = _param) do
    events = Events.search_events(query)

    case events do
      [] ->
        json(conn, %{
          results: [
            %{
              hits: [],
              nbHits: 25,
              page: 0,
              nbPages: 2,
              hitsPerPage: 20,
              exhaustiveNbHits: false,
              exhaustiveTypo: true,
              exhaustive: %{nbHits: false, typo: true},
              query: query,
              params: "[]",
              index: index,
              renderingContent: %{},
              processingTimeMS: 1,
              serverTimeMS: 2,
              processingTimingsMS: %{_request: %{roundTrip: 7}, getIdx: %{load: %{total: 1}, total: 1}, total: 1}
            }
          ]
        })

      events ->
        event_dto = EventsServiceWeb.EventJSON.to_search_event_dto(%{events: events})

        json(conn, %{
          results: [
            %{
              hits: event_dto,
              nbHits: 25,
              page: 0,
              nbPages: 2,
              hitsPerPage: 20,
              exhaustiveNbHits: false,
              exhaustiveTypo: true,
              exhaustive: %{nbHits: false, typo: true},
              query: query,
              params: "[]",
              index: index,
              renderingContent: %{},
              processingTimeMS: 1,
              serverTimeMS: 2,
              processingTimingsMS: %{_request: %{roundTrip: 7}, getIdx: %{load: %{total: 1}, total: 1}, total: 1}
            }
          ]
        })
    end
  end

  def search(conn, _param) do
    json(conn, %{
      results: [
        %{
          hits: [],
          nbHits: 25,
          page: 0,
          nbPages: 2,
          hitsPerPage: 20,
          exhaustiveNbHits: false,
          exhaustiveTypo: true,
          exhaustive: %{nbHits: false, typo: true},
          query: "",
          params: "[]",
          index: "",
          renderingContent: %{},
          processingTimeMS: 1,
          serverTimeMS: 2,
          processingTimingsMS: %{
            _request: %{roundTrip: 7},
            getIdx: %{load: %{total: 1}, total: 1},
            total: 1
          }
        }
      ]
    })
  end

  operation :create,
    summary: "Create a new event",
    parameters: [],
    request_body: {"Event params", "application/json", EventParams},
    responses: %{
      :ok => {"Event response", "application/json", EventResponse},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"},
      :unprocessable_entity => %Reference{"$ref": "#/components/responses/unprocessable_entity"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def create(%{assigns: %{current_user_id: user_id}} = conn, event_params) do
    with {_, %{valid?: true}} <- {:params, EventCreationValidator.create_event_params(event_params)},
         {_, {:ok, %Event{id: event_id}}} <- {:create_event, Events.create_event(user_id, event_params)},
         {_, %Event{} = event} <- {:get_event, Event.get(event_id, [:donations, :artists])} do
      Logger.info("Event #{event_id} sucessfully created by user #{user_id}")

      conn
      |> put_status(:ok)
      |> render(:create, event: event)
    else
      {:params, %{valid?: false} = changeset} ->
        Logger.error("Event creation failed with error #{inspect(changeset.errors)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :event_creation_failed,
          message: ChangesetJSON.error_to_text(%{changeset: changeset})
        })

      {:create_event, {:error, failed_value}} ->
        Logger.error("Event creation failed with error #{inspect(failed_value)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :event_creation_failed,
          message: "#{inspect(failed_value)}"
        })

      {:get_event, nil} ->
        Logger.critical("Event could not be found after creation")

        conn
        |> put_status(:internal_server_error)
        |> json(%{message: "Event not found", error_code: :internal_server_error})
    end
  end

  def show_edit(conn, %{"id" => id}) do
    # "event.view" permission check is not sufficient in this case, as Users with assigned
    # security role, still migth fetch ALL event details.
    # TODO SD1-2174

    with {_, true} <-
           {:has_access?, event_access?(conn, id, "event.view")},
         {_, %Event{chart_key: chart_key} = event} when not is_nil(chart_key) <-
           {:get_event, Events.get_edit_event(id)},
         {_, {:ok, {_public_key, private_key}}} <-
           {:fetch_workspace_keys, Seatsio.fetch_active_workspace_keys_by_event(event)} do
      chart =
        Seatsio.retrieve_chart(chart_key, with_categories: true, with_reports: true, private_workspace_key: private_key)

      event = Map.put(event, :chart, chart)
      render(conn, :edit_event_dto, event: event)
    else
      {:has_access?, false} ->
        conn |> put_status(:forbidden) |> text("User is not authorized to access event #{id}.")

      {:get_event, nil} ->
        conn |> put_status(:not_found) |> text("Event not found.")

      {:get_event, %Event{chart_key: nil} = event} ->
        render(conn, :edit_event_dto, event: event)

      {:fetch_workspace_keys, {:error, msg}} ->
        Logger.error("Error retrieving workspace keys: #{inspect(msg)}")
        conn |> put_status(:internal_server_error) |> text("Error retrieving workspace keys.")
    end
  end

  def show(conn, %{"id" => id, "scope" => "service"}) do
    [api_token | _] = get_req_header(conn, "x-api-token")
    Logger.debug("EventController show with scope=service")
    Logger.debug("api_token: #{inspect(api_token)}")

    case ExServiceClient.Token.verify_and_validate(api_token) do
      {:ok, _claim} ->
        case Events.get_show_event(id) do
          nil ->
            Logger.error("Can't find event with id #{inspect(id)} in the database!")

            conn
            |> put_status(:not_found)
            |> json(%{error: :unknown_event_id})

          event ->
            data =
              EventsServiceWeb.EventJSON.to_internal_event_details_dto(%{
                event: event
              })

            Logger.debug("Get event #{inspect(data)} for internal use.")

            conn
            |> put_status(:ok)
            |> json(data)
        end

      {:error, error} ->
        Logger.error("Service authentication failed because of #{inspect(error)}")

        conn
        |> put_status(:forbidden)
        |> json(%{error: error})
    end
  end

  def show(conn, %{"id" => id}) do
    id
    |> Ecto.UUID.cast()
    |> case do
      {:ok, _} -> Events.get_show_public_event(id)
      :error -> Events.get_show_public_event_by_short_code(id)
    end
    |> case do
      %Event{chart_key: nil} = event ->
        %{event: event}
        |> EventsServiceWeb.EventJSON.to_public_event_details_dto()
        |> then(&json(conn, &1))

      %Event{chart_key: chart_key} = event ->
        case Seatsio.fetch_active_workspace_keys_by_event(event) do
          {:ok, {_public_key, private_key}} ->
            case Seatsio.retrieve_chart(chart_key,
                   with_categories: true,
                   with_reports: true,
                   private_workspace_key: private_key
                 ) do
              {:error, error} ->
                Logger.error("Failed to retrieve chart: #{inspect(error)}")

                conn
                |> put_status(:internal_server_error)
                |> json(%{error: "Failed to retrieve chart data"})

              chart ->
                event
                |> Map.put(:chart, chart)
                |> then(&%{event: &1})
                |> EventsServiceWeb.EventJSON.to_public_event_details_dto()
                |> then(&json(conn, &1))
            end

          {:error, error} ->
            Logger.error("Failed to fetch workspace keys: #{inspect(error)}")

            conn
            |> put_status(:internal_server_error)
            |> json(%{error: "Failed to fetch workspace keys"})
        end

      _ ->
        conn
        |> put_status(:not_found)
        |> json("Der Datenbankeintrag wurde nicht gefunden.")
    end
  end

  @spec publish(%{:assigns => map(), optional(any()) => any()}, any()) :: Plug.Conn.t()
  def publish(%{assigns: %{current_user_id: user_id, user_payload: %{"email" => email}}} = conn, %{
        "event_id" => event_id
      }) do
    with {_, true} <- {:permission, event_access?(conn, event_id, "event.edit")},
         {_, :ok} <- {:onboarded, Vendor.validate_legal_entity(user_id)},
         {_, %Event{} = event} <- {:event, Events.get_edit_event(event_id)},
         {_, {:ok, %Event{id: event_id} = event}} <- {:publish, Events.publish_draft_event(event)} do
      # Create a job to send an email to the promoter
      Events.insert_event_creation_mail_job(email, event_id)
      Logger.debug("Event #{inspect(event)} saved in the database.")
      # Publish event creation message to pubsub topic for other services e.g. com-service (for event conversation)
      EventPublicationHelper.publish_event_creation_msg(%{event_id: event_id})

      conn
      |> put_status(:ok)
      |> render(:edit_event_dto, event: event)
    else
      {:permission, _} ->
        conn
        |> put_status(:forbidden)
        |> json(%{error_code: :forbidden, message: "Forbidden"})

      {:event, _} ->
        conn
        |> put_status(:not_found)
        |> json(%{error_code: :not_found, message: "Event not found"})

      {:publish, {:error, :no_draft}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error_code: :unprocessable_entity, message: "Event is not in draft mode."})

      {:publish, {:error, :event_start_date_not_in_future}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :unprocessable_entity,
          message: "Event start date must be in the future to publish."
        })

      {:publish, {:error, :event_admission_date_not_in_future}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :unprocessable_entity,
          message: "Event admission date must be empty or in the future to publish."
        })

      {:publish, {:error, :closed_event}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error_code: :unprocessable_entity, message: "Event is already closed."})

      {:onboarded, _error} ->
        conn
        |> put_status(:forbidden)
        |> json(%{error_code: :forbidden, message: "Promoter is not onboarded."})

      _ ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{error_code: :internal_server_error, message: "Failed to publish event."})
    end
  end

  def publish(conn, _params) do
    conn
    |> put_status(:unprocessable_entity)
    |> json(%{error_code: :unprocessable_entity, message: "Failed to publish event. Missing event id or user auth."})
  end

  def promoter_details(conn, %{"event_id" => event_id}) do
    if event_access?(conn, event_id, "event.view") do
      case Events.get_promoter_details_event(event_id) do
        nil ->
          conn |> put_status(:not_found) |> text("Der Datenbankeintrag wurde nicht gefunden.")

        event ->
          render(conn, :promoter_details_dto, event: event)
      end
    else
      conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")
    end
  end

  def event_promoter(conn, %{"event_id" => event_id}) do
    if event_access?(conn, event_id, "event.edit") do
      case Events.get_event_promoter(event_id) do
        nil ->
          conn |> put_status(:not_found) |> json("Der Datenbankeintrag wurde nicht gefunden.")

        %{is_draft: _is_draft, promoter: %Promoter{}} = event ->
          promoter = EventsServiceWeb.PromoterJSON.to_promoter_dto(event)
          json(conn, %{data: promoter})
      end
    else
      conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")
    end
  end

  # This function is used by order service to fetch promoter for a given event
  def promoter_event_service(conn, %{"id" => event_id, "scope" => "service"}) do
    [api_token | _] = get_req_header(conn, "x-api-token")

    case ExServiceClient.Token.verify_and_validate(api_token) do
      {:ok, _claim} ->
        case Events.get_event_promoter(event_id) do
          nil ->
            conn |> put_status(:not_found) |> json("Der Datenbankeintrag wurde nicht gefunden.")

          %{is_draft: _is_draft, promoter: %Promoter{}} = event ->
            data = EventsServiceWeb.PromoterJSON.to_promoter_dto(event)

            json(conn, %{data: data})
        end

      {:error, error} ->
        Logger.error("Service authentication failed because auf #{inspect(error)}")

        conn
        |> put_status(:forbidden)
        |> json(%{error: error})
    end
  end

  def approve(conn, %{"approved" => approved, "id" => event_id}) do
    with {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")},
         {_, {:ok, %Event{} = _event}} <- {:approve_event, Events.approve_event(event_id, %{"is_approved" => approved})} do
      updated_event = Events.get_edit_event(event_id)
      render(conn, :edit_event_dto, event: updated_event)
    else
      {:access_validation, false} ->
        conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")

      {:approve_event, {:error, error}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :approve_event_error,
          message: "Failed to approve event with error: #{inspect(ChangesetJSON.error_to_text(error))}"
        })
    end
  end

  def visible(conn, %{"visible" => visible, "id" => event_id}) do
    with {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")},
         {_, {:ok, %Event{} = _event}} <- {:publish_event, Events.publish_event(event_id, %{"is_visible" => visible})} do
      updated_event = Events.get_edit_event(event_id)
      render(conn, :edit_event_dto, event: updated_event)
    else
      {:access_validation, false} ->
        conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")

      {:publish_event, {:error, error}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :publish_event_error,
          message: "Failed to publish event with error: #{inspect(ChangesetJSON.error_to_text(error))}"
        })
    end
  end

  # This function is call when the promoter click the approve link send after event creation
  def promoter_approve_event(conn, %{"action" => action, "id" => event_id, "code" => code}) do
    case action do
      "approve" ->
        with {_, hmac} <- {:get_event_id_hash, Events.EventHelpers.get_event_id_hash(event_id)},
             {_, %Event{} = _event} <- {:get_event, Events.get_event(event_id)},
             {_, true} <- {:compare_hash, hmac == code},
             {_, _updated_event} <-
               {:approve_event, Events.approve_event(event_id, %{"is_approved" => true})} do
          conn |> put_status(:ok) |> json(%{status: ~c"ok"})
        else
          {:get_event_id_hash, _} ->
            conn |> put_status(:ok) |> json("Action #{action} is not supported")

          {:get_event, _} ->
            conn |> put_status(:not_found) |> json("Der Datenbankeintrag wurde nicht gefunden.")

          {:compare_hash, _} ->
            conn |> put_status(:bad_request) |> json("Code is not valid for event")

          {operation, {:error, msg}} ->
            {:error, {operation, msg}}
        end

      _ ->
        conn |> put_status(:ok) |> json("Action #{action} is not supported")
    end
  end

  def update(conn, %{"id" => event_id} = params) do
    with {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")},
         {_, %Event{} = event} <- {:get_edit_event, Events.get_edit_event(event_id)},
         {_, {:ok, %Event{} = _event}} <- {:update_event, Events.update_event(event, params)},
         {_, %Event{} = updated_event} <- {:get_updated_event, Events.get_edit_event(event_id)} do
      data =
        %{event: updated_event}
        |> EventsServiceWeb.EventJSON.to_internal_event_details_dto()
        |> Map.put(:event_id, event_id)

      EventPublicationHelper.publish_event_update_msg(data)
      render(conn, :edit_event_dto, event: updated_event)
    else
      {:access_validation, false} ->
        conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")

      {:get_edit_event, _} ->
        conn |> put_status(:not_found) |> text("Event not found.")

      {:update_event, {:error, :closed_event}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :update_event_error,
          message: "Failed to update event with error: event closed"
        })

      {:update_event, {:error, error}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :update_event_error,
          message: "Failed to update event with error: #{inspect(ChangesetJSON.error_to_text(error))}"
        })

      {:get_updated_event, _} ->
        Logger.critical("Event could not be found after update")

        conn
        |> put_status(:internal_server_error)
        |> json(%{message: "Event not found", error_code: :internal_server_error})
    end
  end

  def create_permissions(conn, %{"email" => email, "id" => event_id, "roles" => roles} = params) do
    with {_, true} <-
           {:access, event_access?(conn, event_id, "event.edit")},
         {_, %Event{} = event} <- {:event, Events.get_event(event_id)},
         {_, {:ok, userinfo}} <- {:user, AccountsService.get_user_by_email(email)},
         {_, :ok} <-
           {:validator, EventPermissionValidator.validate_create(userinfo, event, params)},
         {_, {:ok, _response}} <-
           {:update_claims, EventPermissions.update_user_claims(userinfo, roles, [])},
         {_, {:ok, event_permission}} <-
           {:create_permission, EventPermissions.create(params, userinfo, event_id)} do
      event = Events.get_show_event(event_id)
      Mailer.publish_event_permissions_changed_mail(event, userinfo["email"], roles)
      conn |> put_status(:ok) |> json(%{id: event_permission.id})
    else
      {:access, _} ->
        conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")

      {:event, _} ->
        conn |> put_status(:not_found) |> text("Event not found.")

      {:validator, {:error, :user_is_event_owner}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> text("No further permissions can be assigned to the creator of the event.")

      {:validator, {:error, msg}} ->
        conn |> put_status(:unprocessable_entity) |> text("Validation failed: #{inspect(msg)}")

      {:user, error} ->
        Logger.error("User not found when trying to create permissions: #{inspect(error)}")
        conn |> put_status(:not_found) |> text("User not found.")

      {:update_claims, error} ->
        Logger.error("Failed to update user claims when trying to create permissions: #{inspect(error)}")
        conn |> put_status(:internal_server_error) |> text("Failed to update user claims.")

      {:create_permission, error} ->
        Logger.error("Failed to create event permission: #{inspect(error)}")
        conn |> put_status(:internal_server_error) |> text("Failed to create event permission.")
    end
  end

  def update_permissions(
        conn,
        %{"email" => email, "id" => event_id, "permission_id" => permission_id, "roles" => roles} = params
      ) do
    with {_, true} <-
           {:access, event_access?(conn, event_id, "event.edit")},
         {_, {:ok, userinfo}} <-
           {:user, AccountsService.get_user_by_email(email)},
         {_, %EventPermission{} = event_permission} <-
           {:permission, EventPermission.get_event_permission(permission_id, [:event])},
         {_, :ok} <-
           {:validation, EventPermissionValidator.validate_change(event_permission, params)},
         {_, {:ok, _response}} <-
           {:update_claims, EventPermissions.update_user_claims(userinfo, roles, [])},
         {_, {:ok, _event_permission}} <-
           {:update, EventPermissions.update(event_permission, params, userinfo)} do
      event = Events.get_show_event(event_id)
      Mailer.publish_event_permissions_changed_mail(event, userinfo["email"], roles)
      conn |> put_status(:ok) |> json(%{})
    else
      {:access, _} ->
        conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")

      {:user, error} ->
        Logger.error("User not found when trying to update permissions: #{inspect(error)}")
        conn |> put_status(:not_found) |> text("User not found.")

      {:permission, _} ->
        conn |> put_status(:not_found) |> text("Permission not found.")

      {:validation, {:error, msg}} ->
        conn |> put_status(:unprocessable_entity) |> text("Validation failed: #{inspect(msg)}")

      {:update_claims, error} ->
        Logger.error("Failed to update user claims when trying to update permissions: #{inspect(error)}")
        conn |> put_status(:internal_server_error) |> text("Failed to update user claims.")

      {:update, error} ->
        Logger.error("Failed to update event permission: #{inspect(error)}")
        conn |> put_status(:internal_server_error) |> text("Failed to update event permission.")
    end
  end

  def delete_permissions(conn, %{"id" => event_id, "permission_id" => permission_id} = _param) do
    with {_, true} <-
           {:access, event_access?(conn, event_id, "event.edit")},
         {_, %EventPermission{} = event_permission} <-
           {:permission, EventPermission.get_event_permission(permission_id, [:event])},
         {_, :ok} <-
           {:validation, EventPermissionValidator.validate_delete(event_permission)},
         {_, {:ok, userinfo}} <-
           {:user, AccountsService.get_user_by_id(event_permission.user_document_id)},
         {_, {:ok, _deleted_permission}} <-
           {:delete, EventPermissions.delete(event_permission, userinfo)} do
      event = Events.get_show_event(event_id)
      Mailer.publish_event_permissions_changed_mail(event, userinfo["email"], [])
      conn |> put_status(:ok) |> json(%{})
    else
      {:access, _} ->
        conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")

      {:permission, _} ->
        conn |> put_status(:not_found) |> text("Permission not found.")

      {:validation, {:error, msg}} ->
        conn |> put_status(:unprocessable_entity) |> text("Validation failed: #{inspect(msg)}")

      {:user, error} ->
        Logger.error("User not found when trying to delete permissions: #{inspect(error)}")
        conn |> put_status(:not_found) |> text("User not found.")

      {:delete, error} ->
        Logger.error("Failed to delete event permission: #{inspect(error)}")
        conn |> put_status(:internal_server_error) |> text("Failed to delete event permission.")
    end
  end

  def send_sample_ticket_mail(
        %{assigns: %{current_user_id: _user_id}} = conn,
        %{"eventName" => event_name, "email" => email, "data" => data} = _param
      ) do
    with :ok <- Mailer.publish_sample_ticket_mail(email, "Max Mustermann", event_name, data) do
      conn |> put_status(:ok) |> json(%{status: "ok"})
    end
  end

  def send_event_creation_mail(conn, %{"event_id" => event_id, "email" => email, "data" => data} = _params) do
    if event_access?(conn, event_id, "event.edit") do
      event = Events.get_edit_event(event_id, nil)
      security_permission = Events.get_security_user_by_event_id(event_id)
      Mailer.publish_event_creation_mail(email, event, security_permission, data)
      conn |> put_status(:ok) |> json(%{status: "ok"})
    else
      conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")
    end
  end

  def send_admission_control_mail(
        %{assigns: %{current_user_id: user_id}} = conn,
        %{"id" => event_id, "email" => email} = params
      ) do
    entrance_area_id = params["entranceAreaId"]

    with {_, true} <-
           {:access_validation, event_access?(conn, event_id, "event.view")},
         {_, {:ok, %{"firstName" => name} = _userinfo}} <-
           {:get_user_by_id, AccountsService.get_user_by_id(user_id)},
         {_, %Event{} = event} <-
           {:get_edit_event, Events.get_edit_event(event_id)},
         {_, %{user_document_id: user_document_id} = _security_permission} <-
           {:get_security_user_by_event_id, Events.get_security_user_by_event_id(event_id, entrance_area_id)},
         {_, {:ok, custom_token, _claims}} <-
           {:create_custom_token, Token.create_custom_token(event.start_date, event.end_date, user_document_id)} do
      entrance_area = entrance_area_id && EntranceAreas.get(entrance_area_id)
      Mailer.publish_admission_control_mail(email, name, event, custom_token, entrance_area)
      conn |> put_status(:ok) |> json(%{status: "ok"})
    else
      {:access_validation, false} ->
        conn |> put_status(:forbidden) |> text("User is not authorized to access event #{event_id}.")

      {:get_user_by_id, {:ok, _}} ->
        Logger.error("The returned user infos schema is not as expected.")
        conn |> put_status(:internal_server_error) |> text("The returned user infos schema is not as expected.")

      {:get_user_by_id, {:error, _}} ->
        conn |> put_status(:internal_server_error) |> text("User not found.")

      {:get_edit_event, _} ->
        conn |> put_status(:not_found) |> text("Event not found.")

      {:get_security_user_by_event_id, _} ->
        conn |> put_status(:internal_server_error) |> text("Security user not found.")

      {:create_custom_token, {:error, _}} ->
        conn |> put_status(:internal_server_error) |> text("Failed to create custom token.")
    end
  end

  # This function is called by order service to verify that a promoter has checkin permission for a given event
  def access(conn, %{"id" => _event_id, "permission" => nil}) do
    conn |> put_status(:bad_request) |> json(%{error: "Permission is required"})
  end

  def access(conn, %{"id" => event_id, "permission" => permission}) do
    if event_access?(conn, event_id, permission) do
      conn |> put_status(:ok) |> json(%{hasAccess: true})
    else
      conn
      |> put_status(:forbidden)
      |> json("User is not authorized to access event #{event_id}.")
    end
  end

  def calendar(conn, %{"id" => event_id}) do
    with {_, %Event{} = event} <- {:event, Events.get_show_event(event_id)},
         {_, filename} <- {:filename, prepare_filename("#{event.title}.ics")},
         {_, {:ok, _file}} <- {:file, EventsService.Events.EventHelpers.event_to_ics(event, filename)} do
      conn
      |> put_resp_header("content-disposition", ~s(attachment; filename="#{filename}"))
      |> send_file(:ok, filename)
    else
      {:event, error} ->
        Logger.error("Can't read event for #{inspect(event_id)} during ics export because of #{inspect(error)}.")

        conn
        |> put_status(:not_found)
        |> json(%{error_code: :event_not_found, message: "Event for #{inspect(event_id)} not found"})

      {:filename, error} ->
        Logger.error("Unprocessable event for #{inspect(event_id)} during ics export because of #{inspect(error)}.")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error_code: :unprocessable_event_title, message: "Unprocessable event for #{inspect(event_id)}"})

      {:file, error} ->
        Logger.error("Can't write ics export for event #{inspect(event_id)} because of #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{error_code: :file_write_error, message: "Can't write ics export file."})
    end
  end

  operation :execute_script,
    summary: "Executes a script, identified by the script_id",
    request_body: {"url", "application/json"},
    responses: %{
      # TODO: add multipe responses for status 200
      :ok => {"script_response", "application/json", nil},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"}
    }

  def execute_script(conn, %{"action" => "execute", "script_id" => script_id} = params) do
    case script_id do
      "00437349-32da-491f-a7ce-d180d987ecab" ->
        {:ok, response} = conn |> get_user_id() |> Workflow.finalize_events()
        conn |> put_status(:ok) |> json(response)

      "562df3c1-35a5-4c26-aec5-e05692c05224" ->
        Task.start(fn -> Workflow.sync_ticket_counter(params) end)
        conn |> put_status(:ok) |> json(%{})

      "6054eac4-604e-44de-9ac5-bec020abe702" ->
        :ok = Tracking.execute_tracking_link_sync()
        conn |> put_status(:ok) |> json(:ok)

      _ ->
        conn |> put_status(:not_found) |> json(%{error: :unknown_id})
    end
  end

  defp prepare_filename(filename) do
    filename
    |> String.replace("\ ", "_")
    |> String.replace("\"", "'")
    |> String.replace("\\", "-")
    |> String.replace("/", "-")
  end
end
