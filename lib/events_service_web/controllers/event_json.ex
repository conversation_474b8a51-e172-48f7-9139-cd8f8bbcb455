defmodule EventsServiceWeb.EventJSON do
  import EventsService.Events.EventHelpers

  alias EventsService.Events.Event
  alias EventsService.Events.Variant
  alias EventsService.Promotion
  alias EventsService.VariantQuota
  alias EventsServiceWeb.PlatformFeeJson
  alias EventsServiceWeb.VenueJSON

  require Logger

  @doc """
  Renders a list of events (optionally for sellers)
  """
  def index(%{events: events, meta: meta}) do
    Map.put(meta, :data, for(event <- events, do: event_to_json(event)))
  end

  def index(%{events: events}) do
    %{data: for(event <- events, do: data(event))}
  end

  def create(%{event: event}) do
    data(event)
  end

  def to_event_overview_dto(%{events: events}) do
    for event <- events do
      to_event_overview_dto_data(event)
    end
  end

  def to_event_overview_dto(%{page: page}) do
    %{
      data: for(event <- page.entries, do: to_event_overview_dto_data(event)),
      pageNumber: page.page_number,
      pageSize: page.page_size,
      totalPages: page.total_pages,
      totalEntries: page.total_entries
    }
  end

  def to_event_complete_dto(%{events: events}) do
    for event <- events do
      to_event_complete_dto_data(event)
    end
  end

  def to_event_complete_dto(%{page: page}) do
    %{
      data: for(event <- page.entries, do: to_event_complete_dto_data(event)),
      pageNumber: page.page_number,
      pageSize: page.page_size,
      totalPages: page.total_pages,
      totalEntries: page.total_entries
    }
  end

  def to_internal_events_details_dto(%{events: events}) do
    for(event <- events, do: to_internal_event_details_dto(%{event: event}))
  end

  @doc """
  Renders a single event.
  """
  def show(%{event: event}) do
    to_event_details_dto_data(event)
  end

  def to_search_event_dto(%{events: events}) do
    for(event <- events, do: search_event_dto_data(event))
  end

  def to_public_event_details_dto(%{event: event}) do
    to_public_event_details_dto_data(event)
  end

  def to_internal_event_details_dto(%{event: event}) do
    donation = get_donation_recipient(event.donations)
    # workaround for backwards compatibility, will be removed after update the orders-service
    vouchers =
      case Promotion.fetch_vouchers_for_event(event.id) do
        {:ok, vouchers} -> vouchers
        {:error, :event_not_found} -> []
      end

    event_variants = Variant.add_status(event.variants)

    # styler:sort
    %{
      admissionDate: event.admission_date,
      approved: event.is_approved,
      artists: transform_artist_dto(event.artists),
      balanceAccountId: event.balance_account_id,
      boxOfficeOpeningDate: event.box_office_opening_date || event.start_date,
      category: event.category,
      channelConfigs: transform_channel_configs(event.channel_configs),
      chartKey: event.chart_key,
      cheapestPrice: get_cheapest_gross_price(event_variants, event.fees),
      closedDate: event.closed_at,
      coverUrl: get_cover_url(event),
      createdByDocumentId: event.created_by_document_id,
      description: event.description,
      donationAmount: donation.amount,
      donationRecipientId: donation.donation_recipient_id,
      endDate: event.end_date,
      fees: transform_fee_dto(event.fees),
      id: event.id,
      isDraft: event.is_draft,
      isFutureDemand: event.is_future_demand,
      kickback: if(is_nil(event.kickback), do: 0, else: event.kickback / 100),
      planId: event.plan_id,
      platformFees: PlatformFeeJson.show(%{platform_fees: event.platform_fees}),
      promoter: transform_promoter_internal_dto(event.promoter),
      salesStatus: get_sales_status(event_variants),
      shortCode: event.short_code,
      slug: event.slug,
      startDate: event.start_date,
      subtitle: event.subtitle,
      thumbnailPath: get_file_url(event.thumbnail_url),
      thumbnailUrl: get_file_url(event.thumbnail_url),
      ticketColor: event.ticket_color,
      title: event.title,
      trackingLinks: transform_tracking_links(event.tracking_links),
      trackingPixels: transform_tracking_pixels(event.tracking_pixels),
      useEventBalanceAccount: event.use_event_balance_account,
      variants: transform_internal_variants_dto(event),
      # styler:sort
      venue: %{
        city: event.venue.address.locality,
        country: event.venue.address.country.country,
        houseNumber: get_house_number(event.venue.address.street_address),
        id: event.venue.id,
        iso: event.venue.address.country.iso,
        name: event.venue.name,
        permissions: %{},
        street: get_street(event.venue.address.street_address),
        zipCode: event.venue.address.postal_code
      },
      visible: event.is_visible,
      vouchers: transform_vouchers(vouchers)
    }
  end

  @doc """
  Renders a single event.
  """
  def edit_event_dto(%{event: event}) do
    to_edit_event_dto_data(event)
  end

  def promoter_details_dto(%{event: event}) do
    ticket_variants = Enum.filter(event.variants, & &1.ticket_category.admission)
    extra_variants = Enum.reject(event.variants, & &1.ticket_category.admission)
    donation = get_donation_recipient(event.donations)
    permission = transform_event_permissions(event)
    signin_qr_code = create_signin_qr_code(event)

    # styler:sort
    %{
      checkedIn: get_total_checked_in(event.event_counter),
      coverUrl: get_cover_url(event),
      # return a int instead of float for backwarts compability
      donationAmount: round(donation.amount),
      donationRecipientId: donation.donation_recipient_id,
      endDate: event.end_date || DateTime.add(event.start_date, 12, :hour),
      extraSold: get_sold_extras(event.event_counter),
      extrasAvailable: Enum.count(extra_variants) > 0,
      extrasRedeemed: get_redeemed_extras(event.event_counter),
      extrasRemaining: get_remaining_contingent(extra_variants),
      id: event.id,
      isDraft: event.is_draft,
      permissions: permission,
      securityLoginCode: signin_qr_code,
      shortCode: event.short_code,
      slug: event.slug,
      startDate: event.start_date,
      thumbnailUrl: get_file_url(event.thumbnail_url),
      # get_cheapest_ticket_price only works for modified variants with an available flag
      # get_cheapest_gross_price does exactly the same but will return 0 for unmigrated events (ticket automation)
      ticketBasePrice: get_cheapest_gross_price(ticket_variants),
      ticketColor: event.ticket_color,
      ticketQuota: get_event_contingent(event.variants),
      ticketSold: get_sold_tickets(event.event_counter),
      ticketsAvailable: Enum.count(ticket_variants) > 0,
      ticketsRemaining: get_remaining_contingent(ticket_variants),
      title: event.title,
      totalSales: get_total_sales(event.event_counter) / 100,
      trackingLinks: transform_tracking_links(event.tracking_links),
      trackingPixels: transform_tracking_pixels(event.tracking_pixels),
      venueId: event.venue_id,
      visible: event.is_visible
    }
  end

  defp data(%Event{} = event) do
    donation = get_donation_recipient(event.donations)

    maybe_include_end_date(
      # styler:sort
      %{
        admissionDate: event.admission_date,
        artists: transform_artist_dto(event.artists),
        category: event.category,
        chartKey: event.chart_key,
        coverUrl: get_cover_url(event),
        donationAmount: donation.amount,
        donationRecipientId: donation.donation_recipient_id,
        id: event.id,
        startDate: event.start_date,
        subtitle: event.subtitle,
        thumbnailUrl: get_file_url(event.thumbnail_url),
        ticketColor: event.ticket_color,
        title: event.title,
        venueId: event.venue_id
      },
      event
    )
  end

  defp to_public_event_details_dto_data(%Event{} = event) do
    donation = get_donation_recipient(event.donations)
    event_variants = Variant.add_status(event.variants)

    # styler:sort
    %{
      admissionDate: event.admission_date,
      artists: transform_artist_dto(event.artists),
      boxOfficeOpeningDate: event.box_office_opening_date || event.start_date,
      category: event.category,
      chart: event.chart,
      chartKey: event.chart_key,
      cheapestPrice: get_cheapest_gross_price(event_variants, event.fees),
      coverUrl: get_cover_url(event),
      description: event.description,
      donationAmount: donation.amount,
      donationRecipientId: donation.donation_recipient_id,
      endDate: event.end_date,
      id: event.id,
      isDraft: event.is_draft,
      promoter: transform_promoter_basics_dto(event.promoter, event.is_draft),
      salesStatus: get_sales_status(event_variants, event.ticket_categories),
      shortCode: event.short_code,
      slug: event.slug,
      startDate: event.start_date,
      subtitle: event.subtitle,
      thumbnailPath: get_file_url(event.thumbnail_url),
      thumbnailUrl: get_file_url(event.thumbnail_url),
      ticketColor: event.ticket_color,
      title: event.title,
      trackingPixels: transform_tracking_pixels(event.tracking_pixels),
      # styler:sort
      venue: %{
        city: event.venue.address.locality,
        country: event.venue.address.country.country,
        houseNumber: get_house_number(event.venue.address.street_address),
        id: event.venue.id,
        iso: event.venue.address.country.iso,
        name: event.venue.name,
        street: get_street(event.venue.address.street_address),
        zipCode: event.venue.address.postal_code
      }
    }
  end

  defp to_event_details_dto_data(%Event{} = event) do
    donation = get_donation_recipient(event.donations)
    event_variants = Variant.add_status(event.variants)

    # styler:sort
    %{
      admissionDate: event.admission_date,
      approved: event.is_approved,
      artists: transform_artist_dto(event.artists),
      boxOfficeOpeningDate: event.box_office_opening_date || event.start_date,
      category: event.category,
      chartKey: event.chart_key,
      cheapestPrice: get_cheapest_gross_price(event_variants, event.fees),
      coverUrl: get_cover_url(event),
      description: event.description,
      donationAmount: donation.amount,
      donationRecipientId: donation.donation_recipient_id,
      endDate: event.end_date || event.start_date,
      fees: transform_fee_dto(event.fees),
      id: event.id,
      isDraft: event.is_draft,
      permissions: transform_event_permissions(event),
      planId: event.plan_id,
      promoter: transform_promoter_dto(event.promoter),
      salesStatus: get_sales_status(event_variants),
      shortCode: event.short_code,
      slug: event.slug,
      startDate: event.start_date,
      subtitle: event.subtitle,
      thumbnailUrl: get_file_url(event.thumbnail_url),
      ticketColor: event.ticket_color,
      title: event.title,
      trackingLinks: transform_tracking_links(event.tracking_links),
      trackingPixels: transform_tracking_pixels(event.tracking_pixels),
      # styler:sort
      venue: %{
        city: event.venue.address.locality,
        country: event.venue.address.country.country,
        houseNumber: get_house_number(event.venue.address.street_address),
        id: event.venue.id,
        iso: event.venue.address.country.iso,
        name: event.venue.name,
        permissions: %{},
        street: get_street(event.venue.address.street_address),
        zipCode: event.venue.address.postal_code
      },
      visible: event.is_visible
    }
  end

  defp search_event_dto_data(%Event{} = event) do
    event_variants = Variant.add_status(event.variants)

    maybe_include_end_date(
      # styler:sort
      %{
        category: event.category,
        cheapestPrice: get_cheapest_gross_price(event_variants, event.fees),
        id: event.id,
        salesStatus: get_sales_status(event_variants),
        startDate: event.start_date,
        subtitle: event.subtitle,
        thumbnailUrl: get_file_url(event.thumbnail_url),
        title: event.title,
        venue: VenueJSON.to_venue_dto_data(event.venue)
      },
      event
    )
  end

  defp event_to_json(event) do
    venue_city = get_venue_city(event)
    public_variants = Variant.get_public_by_event_id(event.id, nil)
    available_tickets = calculate_available_tickets(%{event | variants: public_variants})

    %{
      id: event.id,
      title: event.title,
      startDate: event.start_date,
      venueCity: venue_city,
      venueName: event.venue && event.venue.name,
      availableTickets: available_tickets
    }
  end

  defp get_venue_city(%{venue: %{address: %{locality: locality}}}) when not is_nil(locality), do: locality
  defp get_venue_city(_), do: nil

  defp calculate_available_tickets(%{variants: variants}) do
    variants
    |> Enum.reduce(%{}, fn %{ticket_category_id: ticket_category_id} = variant, acc ->
      quota = VariantQuota.get_public_remaining_quota(variant) || 0
      Map.update(acc, ticket_category_id, quota, &max(&1, quota))
    end)
    |> Map.values()
    |> Enum.sum()
  end

  defp calculate_available_tickets(_), do: nil

  defp maybe_include_end_date(data, %{end_date: nil}), do: data
  defp maybe_include_end_date(data, %{end_date: end_date}), do: Map.put(data, :endDate, end_date)

  defp to_event_overview_dto_data(%Event{} = event) do
    donation = get_donation_recipient(event.donations)

    event_variants = Variant.add_status(event.variants)

    maybe_include_end_date(
      # styler:sort
      %{
        admissionDate: event.admission_date,
        category: event.category,
        chartKey: event.chart_key,
        cheapestPrice: get_cheapest_gross_price(event_variants, event.fees),
        coverUrl: get_cover_url(event),
        donationAmount: donation.amount,
        donationRecipientId: donation.donation_recipient_id,
        id: event.id,
        isDraft: event.is_draft,
        permissions: %{},
        salesStatus: get_sales_status(event_variants),
        shortCode: event.short_code,
        slug: event.slug,
        startDate: event.start_date,
        subtitle: event.subtitle,
        thumbnailUrl: get_file_url(event.thumbnail_url),
        ticketColor: event.ticket_color,
        title: event.title,
        # styler:sort
        venue: %{
          city: event.venue.address.locality,
          country: event.venue.address.country.country,
          houseNumber: get_house_number(event.venue.address.street_address),
          id: event.venue.id,
          iso: event.venue.address.country.iso,
          name: event.venue.name,
          permissions: %{},
          street: get_street(event.venue.address.street_address),
          zipCode: event.venue.address.postal_code
        },
        venueId: event.venue_id
      },
      event
    )
  end

  defp to_event_complete_dto_data(%Event{} = event) do
    donation = get_donation_recipient(event.donations)

    event_variants = Variant.add_status(event.variants)

    maybe_include_end_date(
      # styler:sort
      %{
        admissionDate: event.admission_date,
        category: event.category,
        chartKey: event.chart_key,
        cheapestPrice: get_cheapest_gross_price(event_variants, event.fees),
        coverUrl: get_cover_url(event),
        description: event.description,
        donationAmount: donation.amount,
        donationRecipientId: donation.donation_recipient_id,
        id: event.id,
        isDraft: event.is_draft,
        permissions: %{},
        salesStatus: get_sales_status(event_variants),
        shortCode: event.short_code,
        slug: event.slug,
        startDate: event.start_date,
        subtitle: event.subtitle,
        thumbnailUrl: get_file_url(event.thumbnail_url),
        ticketColor: event.ticket_color,
        title: event.title,
        # styler:sort
        venue: %{
          city: event.venue.address.locality,
          country: event.venue.address.country.country,
          houseNumber: get_house_number(event.venue.address.street_address),
          id: event.venue.id,
          iso: event.venue.address.country.iso,
          name: event.venue.name,
          permissions: %{},
          street: get_street(event.venue.address.street_address),
          zipCode: event.venue.address.postal_code
        },
        venueId: event.venue_id
      },
      event
    )
  end

  defp to_edit_event_dto_data(%Event{} = event) do
    fees = transform_fee_dto(event.fees)

    maybe_include_end_date(
      # styler:sort
      %{
        admissionDate: event.admission_date || event.start_date,
        approved: event.is_approved,
        artists: transform_artist_dto(event.artists),
        boxOfficeOpeningDate: event.box_office_opening_date,
        category: event.category,
        chart: event.chart,
        chartKey: event.chart_key,
        closedDate: event.closed_at,
        coverPath: get_cover_url(event),
        coverUrl: get_cover_url(event),
        description: event.description,
        donationAmount: 3,
        fees: fees,
        id: event.id,
        isDraft: event.is_draft,
        kickback: if(is_nil(event.kickback), do: 0, else: event.kickback / 100),
        permissions: transform_event_permissions(event),
        planId: event.plan_id,
        promoter: transform_promoter_dto(event.promoter),
        shortCode: event.short_code,
        slug: event.slug,
        startDate: event.start_date,
        subtitle: event.subtitle,
        thumbnailPath: get_file_url(event.thumbnail_url),
        thumbnailUrl: get_file_url(event.thumbnail_url),
        ticketColor: event.ticket_color,
        title: event.title,
        trackingLinks: transform_tracking_links(event.tracking_links),
        trackingPixels: transform_tracking_pixels(event.tracking_pixels),
        useEventBalanceAccount: event.use_event_balance_account,
        venue: %{
          id: event.venue.id,
          name: event.venue.name,
          street: get_street(event.venue.address.street_address),
          houseNumber: get_house_number(event.venue.address.street_address),
          zipCode: event.venue.address.postal_code,
          city: event.venue.address.locality,
          country: event.venue.address.country.country,
          iso: event.venue.address.country.iso,
          permissions: %{}
        },
        venueId: event.venue.id,
        visible: event.is_visible
      },
      event
    )

    # FIXME donationAmount
    # TODO check if venueId is required, already passed in venue object
  end

  defp get_total_sales(nil), do: 0
  defp get_total_sales(event_counter), do: event_counter.total_sales

  defp get_sold_tickets(nil), do: 0
  defp get_sold_tickets(event_counter), do: event_counter.sold_tickets

  defp get_sold_extras(nil), do: 0
  defp get_sold_extras(event_counter), do: event_counter.sold_extras

  defp get_redeemed_extras(nil), do: 0
  defp get_redeemed_extras(event_counter), do: event_counter.redeemed_extras

  defp get_total_checked_in(nil), do: 0
  defp get_total_checked_in(event_counter), do: event_counter.total_checked_in
end
