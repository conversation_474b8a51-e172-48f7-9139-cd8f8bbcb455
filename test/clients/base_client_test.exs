defmodule ExServiceClient.Clients.BaseClientTest do
  use ExUnit.Case, async: true

  # Create a test module that uses BaseClient
  defmodule TestClient do
    use ExServiceClient.Clients.BaseClient
  end

  describe "client/1" do
    test "uses x-api-key header for shortlink endpoint" do
      client = TestClient.client(endpoint: :shortlink)

      # Extract the headers middleware that contains x-api-key
      headers_middleware =
        client.pre
        |> Enum.find(fn
          {Tesla.Middleware.Headers, headers} when is_list(headers) ->
            Keyword.has_key?(headers, :"x-api-key")

          _ ->
            false
        end)

      assert headers_middleware != nil,
             "Expected to find x-api-key header middleware for shortlink endpoint"

      {Tesla.Middleware.Headers, headers} = headers_middleware

      assert Keyword.has_key?(headers, :"x-api-key"),
             "Expected x-api-key to be present in headers"
    end

    test "uses x-api-token header for non-shortlink endpoints" do
      client = TestClient.client(endpoint: :accounts)

      # Extract the headers middleware that contains x-api-token
      headers_middleware =
        client.pre
        |> Enum.find(fn
          {Tesla.Middleware.Headers, headers} when is_list(headers) ->
            Keyword.has_key?(headers, :"x-api-token")

          _ ->
            false
        end)

      assert headers_middleware != nil,
             "Expected to find x-api-token header middleware for accounts endpoint"

      {Tesla.Middleware.Headers, headers} = headers_middleware

      assert Keyword.has_key?(headers, :"x-api-token"),
             "Expected x-api-token to be present in headers"
    end

    test "uses x-api-token header for default endpoint" do
      client = TestClient.client()

      # Extract the headers middleware that contains x-api-token
      headers_middleware =
        client.pre
        |> Enum.find(fn
          {Tesla.Middleware.Headers, headers} when is_list(headers) ->
            Keyword.has_key?(headers, :"x-api-token")

          _ ->
            false
        end)

      assert headers_middleware != nil,
             "Expected to find x-api-token header middleware for default endpoint"

      {Tesla.Middleware.Headers, headers} = headers_middleware

      assert Keyword.has_key?(headers, :"x-api-token"),
             "Expected x-api-token to be present in headers"
    end

    test "does not use x-api-key header for non-shortlink endpoints" do
      client = TestClient.client(endpoint: :accounts)

      # Ensure no x-api-key header middleware exists
      headers_middleware =
        client.pre
        |> Enum.find(fn
          {Tesla.Middleware.Headers, headers} when is_list(headers) ->
            Keyword.has_key?(headers, :"x-api-key")

          _ ->
            false
        end)

      assert headers_middleware == nil,
             "Expected no x-api-key header middleware for accounts endpoint"
    end

    test "does not use x-api-token header for shortlink endpoint" do
      client = TestClient.client(endpoint: :shortlink)

      # Ensure no x-api-token header middleware exists
      headers_middleware =
        client.pre
        |> Enum.find(fn
          {Tesla.Middleware.Headers, headers} when is_list(headers) ->
            Keyword.has_key?(headers, :"x-api-token")

          _ ->
            false
        end)

      assert headers_middleware == nil,
             "Expected no x-api-token header middleware for shortlink endpoint"
    end

    test "uses bearer auth when user_token is provided" do
      client = TestClient.client(authorization: "user-bearer-token", endpoint: :shortlink)

      # Extract the bearer auth middleware
      bearer_middleware =
        client.pre
        |> Enum.find(fn
          {Tesla.Middleware.BearerAuth, _} -> true
          _ -> false
        end)

      assert bearer_middleware != nil,
             "Expected to find BearerAuth middleware when user token is provided"

      {Tesla.Middleware.BearerAuth, opts} = bearer_middleware

      assert opts[:token] == "user-bearer-token",
             "Expected bearer token to match provided authorization"
    end
  end
end
